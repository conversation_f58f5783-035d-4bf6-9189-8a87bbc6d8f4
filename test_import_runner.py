#!/usr/bin/env python3
"""
Unit tests for the import_runner.py script.
Tests the core functionality of converting Trivy and Xray reports to Nessus format.
"""

import pytest
import json
import os
import tempfile
import shutil
import xml.etree.ElementTree as ET
from unittest.mock import patch, mock_open, MagicMock
import sys

# Add the scripts directory to the path so we can import the module
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'opt', 'tenable-importer', 'scripts'))

import import_runner


class TestSeverityMapping:
    """Test the severity mapping function."""
    
    def test_map_severity_to_nessus_critical(self):
        assert import_runner.map_severity_to_nessus("CRITICAL") == "4"
        assert import_runner.map_severity_to_nessus("critical") == "4"
    
    def test_map_severity_to_nessus_high(self):
        assert import_runner.map_severity_to_nessus("HIGH") == "3"
        assert import_runner.map_severity_to_nessus("high") == "3"
    
    def test_map_severity_to_nessus_medium(self):
        assert import_runner.map_severity_to_nessus("MEDIUM") == "2"
        assert import_runner.map_severity_to_nessus("medium") == "2"
    
    def test_map_severity_to_nessus_low(self):
        assert import_runner.map_severity_to_nessus("LOW") == "1"
        assert import_runner.map_severity_to_nessus("low") == "1"
    
    def test_map_severity_to_nessus_unknown(self):
        assert import_runner.map_severity_to_nessus("UNKNOWN") == "0"
        assert import_runner.map_severity_to_nessus("") == "0"
        assert import_runner.map_severity_to_nessus(None) == "0"


class TestUtilityFunctions:
    """Test utility functions."""
    
    @patch('builtins.open', new_callable=mock_open)
    @patch('import_runner.datetime')
    def test_log_message(self, mock_datetime, mock_file):
        """Test that log messages are written with timestamps."""
        mock_datetime.datetime.now.return_value.strftime.return_value = "2023-01-01 12:00:00"
        
        import_runner.log_message("Test message")
        
        mock_file.assert_called_once_with(import_runner.LOG_FILE, 'a', encoding='utf-8')
        mock_file().write.assert_called_once_with("[2023-01-01 12:00:00] Test message\n")
    
    def test_pretty_print_xml(self):
        """Test XML pretty printing."""
        root = ET.Element("test")
        child = ET.SubElement(root, "child")
        child.text = "content"
        
        result = import_runner.pretty_print_xml(root)
        
        assert "<?xml version=" in result
        assert "<test>" in result
        assert "<child>content</child>" in result
    
    @patch('os.path.exists')
    @patch('os.makedirs')
    @patch('shutil.move')
    def test_archive_file(self, mock_move, mock_makedirs, mock_exists):
        """Test file archiving functionality."""
        mock_exists.return_value = False
        filepath = "/test/path/file.json"

        import_runner.archive_file(filepath)

        # Use os.path.join for platform-independent path checking
        expected_archive_dir = os.path.join("/test/path", "archive")
        expected_dest = os.path.join(expected_archive_dir, "file.json")

        mock_makedirs.assert_called_with(expected_archive_dir, exist_ok=True)
        mock_move.assert_called_once_with(filepath, expected_dest)


class TestTrivyConversion:
    """Test Trivy JSON to Nessus XML conversion."""
    
    def test_create_from_trivy_basic(self):
        """Test basic Trivy report conversion."""
        trivy_data = {
            "ArtifactName": "test-image:latest",
            "Results": [
                {
                    "Vulnerabilities": [
                        {
                            "VulnerabilityID": "CVE-2023-1234",
                            "PkgName": "test-package",
                            "Severity": "HIGH",
                            "Description": "Test vulnerability description",
                            "FixedVersion": "1.2.3",
                            "PrimaryURL": "https://example.com/cve-2023-1234"
                        }
                    ]
                }
            ]
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(trivy_data, f)
            temp_path = f.name
        
        try:
            result = import_runner.create_from_trivy(temp_path, "test_report")
            
            # Parse the XML to verify structure
            root = ET.fromstring(result)
            assert root.tag == "NessusClientData_v2"
            
            report = root.find("Report")
            assert report is not None
            assert report.get("name") == "test_report"
            
            report_host = report.find("ReportHost")
            assert report_host is not None
            assert report_host.get("name") == "test-image:latest"
            
            report_item = report_host.find("ReportItem")
            assert report_item is not None
            assert report_item.get("severity") == "3"  # HIGH = 3
            assert report_item.get("pluginName") == "CVE-2023-1234"
            assert report_item.get("pluginFamily") == "Trivy Scan"
            
            description = report_item.find("description")
            assert description is not None
            assert description.text == "Test vulnerability description"
            
            solution = report_item.find("solution")
            assert solution is not None
            assert "Upgrade test-package to version 1.2.3" in solution.text
            
        finally:
            os.unlink(temp_path)
    
    def test_create_from_trivy_empty_results(self):
        """Test Trivy conversion with empty results."""
        trivy_data = {
            "ArtifactName": "clean-image:latest",
            "Results": []
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(trivy_data, f)
            temp_path = f.name
        
        try:
            result = import_runner.create_from_trivy(temp_path, "clean_report")
            
            root = ET.fromstring(result)
            report = root.find("Report")
            report_host = report.find("ReportHost")
            
            # Should have host but no vulnerabilities
            assert report_host is not None
            assert report_host.get("name") == "clean-image:latest"
            assert len(report_host.findall("ReportItem")) == 0
            
        finally:
            os.unlink(temp_path)


class TestXrayConversion:
    """Test Xray JSON to Nessus XML conversion."""
    
    def test_create_from_xray_basic(self):
        """Test basic Xray report conversion."""
        xray_data = {
            "violations": [
                {
                    "component": "test-component:1.0.0",
                    "severity": "CRITICAL",
                    "summary": "Critical vulnerability found",
                    "cves": [
                        {
                            "cve": "CVE-2023-5678",
                            "cvss_v3_score": 9.8
                        }
                    ]
                }
            ]
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(xray_data, f)
            temp_path = f.name
        
        try:
            result = import_runner.create_from_xray(temp_path, "xray_report")
            
            root = ET.fromstring(result)
            assert root.tag == "NessusClientData_v2"
            
            report = root.find("Report")
            assert report.get("name") == "xray_report"
            
            report_host = report.find("ReportHost")
            assert report_host.get("name") == "test-component:1.0.0"
            
            report_item = report_host.find("ReportItem")
            assert report_item.get("severity") == "4"  # CRITICAL = 4
            assert report_item.get("pluginName") == "CVE-2023-5678"
            assert report_item.get("pluginFamily") == "JFrog Xray Scan"
            
            cvss_score = report_item.find("cvss_v3_base_score")
            assert cvss_score is not None
            assert cvss_score.text == "9.8"
            
        finally:
            os.unlink(temp_path)
    
    def test_create_from_xray_multiple_components(self):
        """Test Xray conversion with multiple components."""
        xray_data = {
            "violations": [
                {
                    "component": "component-a:1.0.0",
                    "severity": "HIGH",
                    "summary": "High severity issue",
                    "cves": [{"cve": "CVE-2023-1111"}]
                },
                {
                    "component": "component-b:2.0.0",
                    "severity": "MEDIUM",
                    "summary": "Medium severity issue",
                    "cves": [{"cve": "CVE-2023-2222"}]
                }
            ]
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(xray_data, f)
            temp_path = f.name
        
        try:
            result = import_runner.create_from_xray(temp_path, "multi_component_report")
            
            root = ET.fromstring(result)
            report = root.find("Report")
            
            # Should have two different ReportHost elements
            report_hosts = report.findall("ReportHost")
            assert len(report_hosts) == 2
            
            host_names = [host.get("name") for host in report_hosts]
            assert "component-a:1.0.0" in host_names
            assert "component-b:2.0.0" in host_names
            
        finally:
            os.unlink(temp_path)


class TestTenableSCUpload:
    """Test Tenable.sc upload functionality."""
    
    @patch('import_runner.TenableSC')
    @patch('import_runner.log_message')
    @patch('builtins.open', new_callable=mock_open)
    @patch('os.remove')
    def test_upload_to_tenable_sc_success(self, mock_remove, mock_file, mock_log, mock_tenable):
        """Test successful upload to Tenable.sc."""
        # Mock the TenableSC instance
        mock_sc_instance = MagicMock()
        mock_tenable.return_value = mock_sc_instance
        
        nessus_content = "<NessusClientData_v2></NessusClientData_v2>"
        filename_prefix = "test_report"
        
        result = import_runner.upload_to_tenable_sc(nessus_content, filename_prefix)
        
        assert result is True
        mock_tenable.assert_called_once_with(import_runner.SC_HOST)
        mock_sc_instance.login.assert_called_once_with(
            access_key=import_runner.ACCESS_KEY, 
            secret_key=import_runner.SECRET_KEY
        )
        mock_sc_instance.scan_results.import_scan.assert_called_once()
        mock_sc_instance.logout.assert_called_once()
        mock_remove.assert_called_once_with("test_report.nessus")
    
    @patch('import_runner.TenableSC')
    @patch('import_runner.log_message')
    def test_upload_to_tenable_sc_failure(self, mock_log, mock_tenable):
        """Test failed upload to Tenable.sc."""
        mock_tenable.side_effect = Exception("Connection failed")
        
        result = import_runner.upload_to_tenable_sc("content", "test")
        
        assert result is False
        mock_log.assert_called_with("ERROR: Failed to upload to Tenable.sc. Reason: Connection failed")


class TestMainWorkflow:
    """Test the main workflow function."""
    
    @patch('import_runner.upload_to_tenable_sc')
    @patch('import_runner.archive_file')
    @patch('import_runner.create_from_trivy')
    @patch('import_runner.create_from_xray')
    @patch('import_runner.log_message')
    @patch('os.listdir')
    def test_main_workflow(self, mock_listdir, mock_log, mock_xray, mock_trivy, mock_archive, mock_upload):
        """Test the main workflow processes files correctly."""

        # Mock file listings
        def listdir_side_effect(path):
            if 'trivy' in path:
                return ['trivy_report_1.json', 'trivy_report_2.json', 'not_json.txt']
            elif 'xray' in path:
                return ['xray_report_1.json', 'other_file.log']
            return []

        mock_listdir.side_effect = listdir_side_effect

        # Mock successful conversions and uploads
        mock_trivy.return_value = "<trivy_nessus_xml/>"
        mock_xray.return_value = "<xray_nessus_xml/>"
        mock_upload.return_value = True

        import_runner.main()

        # Verify trivy processing - check that calls were made (exact paths may vary by platform)
        assert mock_trivy.call_count == 2
        trivy_calls = [call.args for call in mock_trivy.call_args_list]
        assert any('trivy_report_1.json' in call[0] and call[1] == 'Trivy_trivy_report_1' for call in trivy_calls)
        assert any('trivy_report_2.json' in call[0] and call[1] == 'Trivy_trivy_report_2' for call in trivy_calls)

        # Verify xray processing
        assert mock_xray.call_count == 1
        xray_calls = [call.args for call in mock_xray.call_args_list]
        assert any('xray_report_1.json' in call[0] and call[1] == 'Xray_xray_report_1' for call in xray_calls)

        # Verify uploads
        assert mock_upload.call_count == 3

        # Verify archiving (should happen after successful uploads)
        assert mock_archive.call_count == 3


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
