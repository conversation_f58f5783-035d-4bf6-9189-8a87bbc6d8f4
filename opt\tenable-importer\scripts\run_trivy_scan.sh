#!/bin/bash
# Enhanced Trivy scan script with improved error handling and configuration

# Source configuration
CONFIG_FILE="/opt/tenable-importer/config.py"
BASE_DIR="/opt/tenable-importer"
REPORTS_DIR="$BASE_DIR/reports/trivy"
LOG_DIR="$BASE_DIR/logs"
LOG_FILE="$LOG_DIR/trivy_scan.log"

# Function to log messages
log_message() {
    local timestamp=$(date "+%Y-%m-%d %H:%M:%S")
    local message="[$timestamp] $1"
    echo "$message"
    
    # Ensure log directory exists
    if [ ! -d "$LOG_DIR" ]; then
        mkdir -p "$LOG_DIR" 2>/dev/null
        if [ $? -ne 0 ]; then
            echo "ERROR: Failed to create log directory: $LOG_DIR"
            return 1
        fi
    fi
    
    echo "$message" >> "$LOG_FILE"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Parse command line arguments
IMAGE_NAME=""
SEVERITY="UNKNOWN,LOW,MEDIUM,HIGH,CRITICAL"
FORMAT="json"
TIMEOUT="300s"

# Show usage information
show_usage() {
    echo "Usage: $0 [options]"
    echo "Options:"
    echo "  -i, --image IMAGE     Container image to scan (required if not using config)"
    echo "  -s, --severity LEVEL  Severity levels to scan for (default: $SEVERITY)"
    echo "  -f, --format FORMAT   Output format (default: $FORMAT)"
    echo "  -t, --timeout TIME    Scan timeout (default: $TIMEOUT)"
    echo "  -h, --help            Show this help message"
    echo ""
    echo "Example: $0 --image nginx:latest"
}

# Parse arguments
while [[ $# -gt 0 ]]; do
    key="$1"
    case $key in
        -i|--image)
        IMAGE_NAME="$2"
        shift 2
        ;;
        -s|--severity)
        SEVERITY="$2"
        shift 2
        ;;
        -f|--format)
        FORMAT="$2"
        shift 2
        ;;
        -t|--timeout)
        TIMEOUT="$2"
        shift 2
        ;;
        -h|--help)
        show_usage
        exit 0
        ;;
        *)
        echo "Unknown option: $1"
        show_usage
        exit 1
        ;;
    esac
done

# Function to extract config value using Python
extract_config_value() {
    local key="$1"
    if [ -f "$CONFIG_FILE" ]; then
        python3 -c "
import sys
sys.path.append('/opt/tenable-importer')
try:
    from config import $key
    print($key)
except (ImportError, AttributeError):
    pass
" 2>/dev/null
    fi
}

# If no image specified via args, try to get from config file
if [ -z "$IMAGE_NAME" ]; then
    if [ -f "$CONFIG_FILE" ]; then
        DEFAULT_IMAGE=$(extract_config_value "DEFAULT_TRIVY_IMAGE")
        if [ -n "$DEFAULT_IMAGE" ]; then
            IMAGE_NAME="$DEFAULT_IMAGE"
            log_message "Using default image from config: $IMAGE_NAME"
        else
            log_message "ERROR: No default image found in config file"
            show_usage
            exit 1
        fi
    else
        log_message "ERROR: No image specified and config file not found"
        show_usage
        exit 1
    fi
fi

# Ensure trivy is installed
if ! command_exists trivy; then
    log_message "ERROR: Trivy is not installed. Please install it first."
    exit 1
fi

# Ensure the reports directory exists
if [ ! -d "$REPORTS_DIR" ]; then
    log_message "Creating reports directory: $REPORTS_DIR"
    mkdir -p "$REPORTS_DIR"
    if [ $? -ne 0 ]; then
        log_message "ERROR: Failed to create reports directory: $REPORTS_DIR"
        exit 1
    fi
fi

# Generate a report name with timestamp
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
IMAGE_SAFE_NAME=$(echo "$IMAGE_NAME" | tr ':/' '_')
REPORT_NAME="trivy_${IMAGE_SAFE_NAME}_${TIMESTAMP}.json"
REPORT_PATH="${REPORTS_DIR}/${REPORT_NAME}"

# Run the scan
log_message "Starting Trivy scan of $IMAGE_NAME (saving to $REPORT_NAME)"
trivy image --timeout "$TIMEOUT" --severity "$SEVERITY" --format "$FORMAT" --output "$REPORT_PATH" "$IMAGE_NAME"

# Check if scan was successful
if [ $? -eq 0 ]; then
    log_message "Scan completed successfully: $REPORT_PATH"
    # Check if file was created and has content
    if [ -f "$REPORT_PATH" ] && [ -s "$REPORT_PATH" ]; then
        log_message "Report generated successfully ($(du -h "$REPORT_PATH" | cut -f1) bytes)"
        echo "Scan complete. Report saved to: $REPORT_PATH"
        exit 0
    else
        log_message "ERROR: Report file is empty or wasn't created"
        exit 1
    fi
else
    log_message "ERROR: Trivy scan failed with error code $?"
    exit 1
fi
