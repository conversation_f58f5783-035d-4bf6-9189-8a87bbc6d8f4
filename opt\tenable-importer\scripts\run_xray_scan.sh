#!/bin/bash
# Enhanced JFrog Xray scan script with improved error handling and configuration

# Source configuration from Python file
CONFIG_FILE="/opt/tenable-importer/config.py"
BASE_DIR="/opt/tenable-importer"
REPORTS_DIR="$BASE_DIR/reports/xray"
LOG_DIR="$BASE_DIR/logs"
LOG_FILE="$LOG_DIR/xray_scan.log"

# Function to log messages
log_message() {
    local timestamp=$(date "+%Y-%m-%d %H:%M:%S")
    local message="[$timestamp] $1"
    echo "$message"
    
    # Ensure log directory exists
    if [ ! -d "$LOG_DIR" ]; then
        mkdir -p "$LOG_DIR" 2>/dev/null
        if [ $? -ne 0 ]; then
            echo "ERROR: Failed to create log directory: $LOG_DIR"
            return 1
        fi
    fi
    
    echo "$message" >> "$LOG_FILE"
}

# Default values - will be overridden by config file if exists
JFROG_URL="https://your-jfrog-instance"
JFROG_USER="your_user"
JFROG_TOKEN="your_token"
BUILD_PATTERN="your-build-name/*"
REPORT_NAME="xray_report_$(date +%Y%m%d_%H%M%S).json"
REPORT_PATH="${REPORTS_DIR}/${REPORT_NAME}"

# Function to extract value from Python config
extract_config_value() {
    local key="$1"
    local default="$2"
    if [ -f "$CONFIG_FILE" ]; then
        local value=$(python3 -c "
import sys
sys.path.append('/opt/tenable-importer')
try:
    from config import $key
    print($key)
except (ImportError, AttributeError):
    print('$default')
" 2>/dev/null)
        if [ -n "$value" ] && [ "$value" != "$default" ]; then
            echo "$value"
        else
            echo "$default"
        fi
    else
        echo "$default"
    fi
}

# Show usage
show_usage() {
    echo "Usage: $0 [options]"
    echo "Options:"
    echo "  -u, --url URL         JFrog URL (default from config)"
    echo "  -n, --user USER       JFrog username (default from config)"
    echo "  -t, --token TOKEN     JFrog API token (default from config)"
    echo "  -b, --build PATTERN   Build pattern to scan (default from config)"
    echo "  -o, --output FILE     Output file name (default: auto-generated)"
    echo "  -h, --help            Show this help message"
    echo ""
    echo "Example: $0 --build 'my-app/*' --output custom_report.json"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    key="$1"
    case $key in
        -u|--url)
        JFROG_URL="$2"
        shift 2
        ;;
        -n|--user)
        JFROG_USER="$2"
        shift 2
        ;;
        -t|--token)
        JFROG_TOKEN="$2"
        shift 2
        ;;
        -b|--build)
        BUILD_PATTERN="$2"
        shift 2
        ;;
        -o|--output)
        REPORT_NAME="$2"
        REPORT_PATH="${REPORTS_DIR}/${REPORT_NAME}"
        shift 2
        ;;
        -h|--help)
        show_usage
        exit 0
        ;;
        *)
        echo "Unknown option: $1"
        show_usage
        exit 1
        ;;
    esac
done

# Load configuration from config file if present
if [ -f "$CONFIG_FILE" ]; then
    log_message "Loading configuration from $CONFIG_FILE"
    JFROG_URL=$(extract_config_value "JFROG_URL" "$JFROG_URL")
    JFROG_USER=$(extract_config_value "JFROG_USER" "$JFROG_USER")
    JFROG_TOKEN=$(extract_config_value "JFROG_TOKEN" "$JFROG_TOKEN")
    BUILD_PATTERN=$(extract_config_value "JFROG_BUILD_NAME" "$BUILD_PATTERN")
fi

# Validate required parameters
if [ "$JFROG_URL" = "https://your-jfrog-instance" ] || [ -z "$JFROG_URL" ]; then
    log_message "ERROR: JFrog URL not configured. Please update config.py or provide via command line."
    exit 1
fi

if [ "$JFROG_USER" = "your_user" ] || [ -z "$JFROG_USER" ]; then
    log_message "ERROR: JFrog username not configured. Please update config.py or provide via command line."
    exit 1
fi

if [ "$JFROG_TOKEN" = "your_token" ] || [ -z "$JFROG_TOKEN" ]; then
    log_message "ERROR: JFrog token not configured. Please update config.py or provide via command line."
    exit 1
fi

# Ensure reports directory exists
if [ ! -d "$REPORTS_DIR" ]; then
    log_message "Creating reports directory: $REPORTS_DIR"
    mkdir -p "$REPORTS_DIR"
    if [ $? -ne 0 ]; then
        log_message "ERROR: Failed to create reports directory: $REPORTS_DIR"
        exit 1
    fi
fi

# Check if curl is available
if ! command -v curl &>/dev/null; then
    log_message "ERROR: curl command not found. Please install curl."
    exit 1
fi

# Create a temporary request file
TEMP_REQUEST=$(mktemp)
if [ $? -ne 0 ]; then
    log_message "ERROR: Failed to create temporary file."
    exit 1
fi

# Create JSON request body
cat > "$TEMP_REQUEST" << EOF
{
  "name": "temp-vulnerability-report-$(date +%s)",
  "resources": {
    "builds": {
      "include_patterns": ["$BUILD_PATTERN"]
    }
  },
  "filters": {
    "min_severity": "low"
  }
}
EOF

log_message "Starting JFrog Xray scan for build pattern: $BUILD_PATTERN"
log_message "Report will be saved to: $REPORT_PATH"

# Execute the API request with proper error handling
HTTP_CODE=$(curl -s -o "$REPORT_PATH" -w "%{http_code}" \
    -u "$JFROG_USER:$JFROG_TOKEN" \
    -X POST "$JFROG_URL/xray/api/v1/reports/vulnerabilities" \
    -H "Content-Type: application/json" \
    -d @"$TEMP_REQUEST")

# Clean up temp file
rm -f "$TEMP_REQUEST"

# Check response code
if [ "$HTTP_CODE" -ge 200 ] && [ "$HTTP_CODE" -lt 300 ]; then
    # Check if file was created and has content
    if [ -f "$REPORT_PATH" ] && [ -s "$REPORT_PATH" ]; then
        log_message "SUCCESS: Report generated successfully ($(du -h "$REPORT_PATH" | cut -f1) bytes)"
        # Check if the file appears to be valid JSON
        if grep -q "violations" "$REPORT_PATH"; then
            log_message "Report validation passed - contains vulnerability data"
            echo "Scan complete. Report saved to: $REPORT_PATH"
            exit 0
        else
            log_message "WARNING: Report may not contain expected data structure"
            echo "Scan completed, but report may not contain vulnerability data."
            exit 0
        fi
    else
        log_message "ERROR: Report file is empty or wasn't created"
        exit 1
    fi
else
    log_message "ERROR: API request failed with HTTP code $HTTP_CODE"
    # Try to get error message from response
    if [ -f "$REPORT_PATH" ]; then
        ERROR_MSG=$(grep -o '"message":"[^"]*"' "$REPORT_PATH" 2>/dev/null)
        if [ -n "$ERROR_MSG" ]; then
            log_message "Error details: $ERROR_MSG"
        fi
        # Remove failed report file
        rm -f "$REPORT_PATH"
    fi
    exit 1
fi
