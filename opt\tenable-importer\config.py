#!/usr/bin/env python3
"""
Configuration file for the Tenable Importer
This file contains all the configuration settings for connecting to Tenable.sc and JFrog Xray.

IMPORTANT: Update all the placeholder values below with your actual credentials and settings.
"""

import os

# =============================================================================
# TENABLE.SC CONFIGURATION
# =============================================================================

# Tenable.sc server hostname or IP address (without https://)
SC_HOST = "your-tenable-sc-server.com"

# Tenable.sc API credentials
# These should be API keys for a user with scan import permissions
ACCESS_KEY = "your_access_key_here"
SECRET_KEY = "your_secret_key_here"

# Repository ID in Tenable.sc where scans will be imported
# You can find this in the Tenable.sc web interface under Repositories
REPO_ID = "1"  # Usually "1" for the default repository

# =============================================================================
# JFROG XRAY CONFIGURATION
# =============================================================================

# JFrog instance URL (include https://)
JFROG_URL = "https://your-jfrog-instance.jfrog.io"

# JFrog credentials
JFROG_USER = "your_jfrog_username"
JFROG_TOKEN = "your_jfrog_api_token"  # Preferred over password

# Default build pattern for Xray scans
# This can be overridden via command line arguments
JFROG_BUILD_NAME = "your-build-name/*"

# =============================================================================
# TRIVY CONFIGURATION
# =============================================================================

# Default container image to scan if none specified
DEFAULT_TRIVY_IMAGE = "nginx:latest"

# =============================================================================
# DIRECTORY CONFIGURATION
# =============================================================================

# Base directory for the importer (should match your installation path)
BASE_DIR = "/opt/tenable-importer"

# Reports directory where JSON reports are stored
REPORTS_DIR = os.path.join(BASE_DIR, "reports")

# Logs directory
LOG_DIR = os.path.join(BASE_DIR, "logs")

# Main log file for the import runner
LOG_FILE = os.path.join(LOG_DIR, "importer.log")

# =============================================================================
# ADVANCED CONFIGURATION
# =============================================================================

# Maximum file size for reports (in bytes) - 50MB default
MAX_REPORT_SIZE = 50 * 1024 * 1024

# Number of days to keep archived reports (0 = keep forever)
ARCHIVE_RETENTION_DAYS = 30

# Enable debug logging (True/False)
DEBUG_LOGGING = False

# Timeout for API requests (in seconds)
API_TIMEOUT = 300

# =============================================================================
# VALIDATION
# =============================================================================

def validate_config():
    """
    Validates the configuration settings.
    Returns a list of validation errors, empty if all is well.
    """
    errors = []
    
    # Check Tenable.sc settings
    if SC_HOST == "your-tenable-sc-server.com":
        errors.append("SC_HOST must be updated with your actual Tenable.sc server")
    
    if ACCESS_KEY == "your_access_key_here":
        errors.append("ACCESS_KEY must be updated with your actual API access key")
    
    if SECRET_KEY == "your_secret_key_here":
        errors.append("SECRET_KEY must be updated with your actual API secret key")
    
    # Check JFrog settings
    if JFROG_URL == "https://your-jfrog-instance.jfrog.io":
        errors.append("JFROG_URL must be updated with your actual JFrog instance URL")
    
    if JFROG_USER == "your_jfrog_username":
        errors.append("JFROG_USER must be updated with your actual JFrog username")
    
    if JFROG_TOKEN == "your_jfrog_api_token":
        errors.append("JFROG_TOKEN must be updated with your actual JFrog API token")
    
    # Check directory permissions
    if not os.access(os.path.dirname(BASE_DIR), os.W_OK):
        errors.append(f"No write permission for base directory: {BASE_DIR}")
    
    return errors

if __name__ == "__main__":
    # Allow running this file directly to validate configuration
    errors = validate_config()
    if errors:
        print("Configuration validation failed:")
        for error in errors:
            print(f"  - {error}")
        print("\nPlease update the configuration values in config.py")
    else:
        print("Configuration validation passed!")
