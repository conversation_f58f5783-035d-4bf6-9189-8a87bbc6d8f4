# Automated Trivy and JFrog Xray Vulnerability Importer for Tenable.sc

This project provides a comprehensive set of scripts to automatically export vulnerability reports from Trivy and JFrog Xray, convert them to the `.nessus` format, and upload them to Tenable.sc. This allows for centralized viewing and management of container and artifact vulnerabilities within Tenable.sc.

## Table of Contents

- [Automated Trivy and JFrog Xray Vulnerability Importer for Tenable.sc](#automated-trivy-and-jfrog-xray-vulnerability-importer-for-tenablesc)
  - [Table of Contents](#table-of-contents)
  - [Objective](#objective)
  - [Features](#features)
  - [How It Works](#how-it-works)
  - [Prerequisites](#prerequisites)
  - [Directory Structure](#directory-structure)
  - [Setup and Configuration](#setup-and-configuration)
  - [Usage](#usage)
    - [Running a Trivy Scan](#running-a-trivy-scan)
    - [Fetching an Xray Report](#fetching-an-xray-report)
    - [Processing and Importing Reports](#processing-and-importing-reports)
  - [Automation with <PERSON><PERSON>](#automation-with-cron)
  - [Logging](#logging)
  - [Troubleshooting](#troubleshooting)
  - [Requirements File](#requirements-file)

## Objective

To create a fully automated, secure, and robust system that regularly imports vulnerability data from JFrog Xray and Trivy scans into Tenable.sc for centralized reporting and analysis.

## Features

- **Multi-Source Integration**: Imports vulnerability data from both Trivy (for container images) and JFrog Xray (for artifacts and builds).
- **Secure Configuration**: Credentials and sensitive data are externalized into a `config.py` file, not hardcoded in scripts.
- **Robust Error Handling**: Scripts include comprehensive logging and error checks to ensure reliability.
- **Automated Workflow**: Designed for easy automation using scheduling tools like `cron`.
- **Data Archiving**: Automatically archives processed reports to prevent re-processing and maintain a clean workspace.
- **Standardized Output**: Converts different JSON report formats into the standard `.nessus` format required by Tenable.sc.

## How It Works

The system follows a simple Extract, Transform, Load (ETL) process:

1. **Extract**: Shell scripts (`run_trivy_scan.sh`, `run_xray_scan.sh`) connect to Trivy or JFrog Xray to generate and download vulnerability reports in JSON format.
2. **Transform**: The main Python script (`import_runner.py`) finds new JSON reports, parses them, and converts the data into a well-structured `.nessus` XML format.
3. **Load**: The `.nessus` data is securely uploaded to a specified repository in Tenable.sc using its API.
4. **Archive**: After a successful upload, the original JSON report is moved to an `archive` subdirectory.
5. **Automate**: The entire workflow is designed to be run on a schedule (e.g., via `cron`) with detailed logs created for every step.

## Prerequisites

Ensure the following are installed and configured on the server that will run the scripts:

- **Operating System**: A Linux-based system.
- **Shell**: `bash`
- **Software**:
  - `python3` and `pip3`
  - `curl`
  - `trivy` (if using the Trivy scanner)
- **Python Libraries**:
  - `tenable-sc` (can be installed via `requirements.txt`)
- **Access & Credentials**:
  - API keys (Access and Secret) for a Tenable.sc user with scan import permissions.
  - An API token or username/password for a JFrog Xray user with reporting permissions.
  - Network connectivity from the script server to Tenable.sc and JFrog Xray instances.

## Directory Structure

```
/opt/tenable-importer/
├── config.py               # Central configuration for all credentials and settings.
├── requirements.txt        # Python package requirements.
├── README.md               # This documentation file.
├── logs/
│   ├── importer.log        # Logs for the main import runner.
│   ├── trivy_scan.log      # Logs for Trivy scan operations.
│   └── xray_scan.log       # Logs for Xray report fetching.
├── reports/
│   ├── trivy/
│   │   └── archive/        # Archived Trivy reports.
│   └── xray/
│       └── archive/        # Archived Xray reports.
└── scripts/
    ├── import_runner.py    # Main Python script to process and upload reports.
    ├── run_trivy_scan.sh   # Script to execute Trivy scans.
    └── run_xray_scan.sh    # Script to fetch reports from JFrog Xray.
```

## Setup and Configuration

1. **Place Files**: Ensure all files are placed in the `/opt/tenable-importer/` directory structure as shown above.

2. **Install Dependencies**: Install the required Python library.

   ```bash
   pip3 install -r /opt/tenable-importer/requirements.txt
   ```

3. **Set Permissions**: Make the scripts executable.

   ```bash
   chmod +x /opt/tenable-importer/scripts/*.py
   chmod +x /opt/tenable-importer/scripts/*.sh
   ```

4. **Create and Configure `config.py`**: The `config.py` file is included with placeholder values. Edit `/opt/tenable-importer/config.py` and update the following settings for your environment:

   **Tenable.sc Settings:**
   - `SC_HOST` - Your Tenable.sc server hostname (without https://)
   - `ACCESS_KEY` - Your Tenable.sc API access key
   - `SECRET_KEY` - Your Tenable.sc API secret key
   - `REPO_ID` - Repository ID where scans will be imported (usually "1")

   **JFrog Xray Settings:**
   - `JFROG_URL` - Your JFrog instance URL (include https://)
   - `JFROG_USER` - Your JFrog username
   - `JFROG_TOKEN` - Your JFrog API token
   - `JFROG_BUILD_NAME` - Default build pattern for scans

   **Trivy Settings:**
   - `DEFAULT_TRIVY_IMAGE` - Default container image to scan

   You can validate your configuration by running:

   ```bash
   python3 /opt/tenable-importer/config.py
   ```

5. **Create Directories**: The scripts will attempt to create necessary directories, but you can also create them manually:

   ```bash
   mkdir -p /opt/tenable-importer/logs
   mkdir -p /opt/tenable-importer/reports/trivy/archive
   mkdir -p /opt/tenable-importer/reports/xray/archive
   ```

## Usage

### Running a Trivy Scan

Execute the script with an image name. It will save the report in `/opt/tenable-importer/reports/trivy/`.

```bash
/opt/tenable-importer/scripts/run_trivy_scan.sh --image your-app:latest
```

### Fetching an Xray Report

Execute the script to fetch a report for the build pattern defined in `config.py` or provided as an argument.

```bash
/opt/tenable-importer/scripts/run_xray_scan.sh --build "my-app-build/*"
```

### Processing and Importing Reports

Run the main importer script. It will automatically find, process, and upload all new reports.

```bash
/opt/tenable-importer/scripts/import_runner.py
```

## Automation with Cron

You can automate the entire process using a `crontab`. Here is an example that runs the scans and imports daily.

```crontab
# Edit crontab with: crontab -e

# Run Trivy scan daily at 2:00 AM
0 2 * * * /opt/tenable-importer/scripts/run_trivy_scan.sh

# Fetch Xray reports daily at 2:15 AM
15 2 * * * /opt/tenable-importer/scripts/run_xray_scan.sh

# Process and import all reports daily at 3:00 AM
0 3 * * * /opt/tenable-importer/scripts/import_runner.py
```

## Logging

All scripts generate detailed logs in the `/opt/tenable-importer/logs/` directory. Check these files to monitor execution and troubleshoot errors.

## Troubleshooting

- **Permission Denied**: Ensure scripts are executable (`chmod +x`) and that the user running them has write permissions for the `logs`, `reports`, and `archive` directories.
- **API Errors**: Check the logs for specific error messages from Tenable.sc or JFrog Xray. This usually indicates incorrect credentials, insufficient permissions, or network issues.
- **Module Not Found**: If you see `ImportError: No module named 'tenable'`, make sure you have installed the dependencies from `requirements.txt`.
- **Configuration Errors**: If you see `Configuration file not found or incomplete`, ensure you have created and properly configured `/opt/tenable-importer/config.py` with your actual credentials.
- **JSON Parsing Errors**: If reports fail to process, check that the JSON files are valid and contain the expected structure. The scripts will log specific error messages to help diagnose issues.

## Requirements File

The `requirements.txt` file is included in `/opt/tenable-importer/` and contains the necessary Python packages:

```text
tenable-sc>=1.4.0
requests>=2.25.0
```
