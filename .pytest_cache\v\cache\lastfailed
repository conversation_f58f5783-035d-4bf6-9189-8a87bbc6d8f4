{"test_import_runner.py::TestUtilityFunctions::test_log_message": true, "test_import_runner.py::TestUtilityFunctions::test_archive_file": true, "test_import_runner.py::TestTrivyConversion::test_create_from_trivy_basic": true, "test_import_runner.py::TestTenableSCUpload::test_upload_to_tenable_sc_success": true, "test_import_runner.py::TestTenableSCUpload::test_upload_to_tenable_sc_failure": true, "test_import_runner.py::TestMainWorkflow::test_main_workflow": true}