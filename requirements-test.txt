# Tenable Importer Requirements
# Core dependencies for the vulnerability import system

# Tenable.sc API client
tenable-sc>=1.4.0

# Standard library dependencies (included with Python 3.6+)
# xml.etree.ElementTree - XML processing
# json - JSON handling
# datetime - Date/time operations
# hashlib - Hash generation
# uuid - UUID generation
# os - Operating system interface
# shutil - File operations
# pathlib - Path handling
# traceback - Error tracing

# Optional dependencies for enhanced functionality
requests>=2.25.0  # HTTP requests (if needed for custom API calls)

# Development and testing dependencies (optional)
pytest>=6.0.0     # Testing framework
pytest-cov>=2.10.0  # Coverage reporting
mock>=4.0.0       # Mocking for tests