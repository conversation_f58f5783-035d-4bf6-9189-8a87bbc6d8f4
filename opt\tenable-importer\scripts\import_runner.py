#!/usr/bin/env python3
# /opt/tenable-importer/scripts/import_runner.py

import json
import os
import shutil
import sys
import xml.etree.ElementTree as ET
from xml.dom import minidom
import datetime
import hashlib
import uuid
import traceback
from pathlib import Path

# Import config from separate file
sys.path.append('/opt/tenable-importer')
try:
    from config import (SC_HOST, ACCESS_KEY, SECRET_KEY, REPO_ID,
                        REPORTS_DIR, LOG_FILE, LOG_DIR)
except ImportError:
    # If we're being imported for testing, use default values
    if __name__ == "__main__":
        print("Error: Configuration file not found or incomplete. Please check /opt/tenable-importer/config.py")
        sys.exit(1)
    else:
        # Default values for testing
        SC_HOST = "test-host"
        ACCESS_KEY = "test-access-key"
        SECRET_KEY = "test-secret-key"
        REPO_ID = "1"
        REPORTS_DIR = "/tmp/test-reports"
        LOG_FILE = "/tmp/test.log"
        LOG_DIR = "/tmp"

# Conditionally import tenable.sc to handle case where it's not installed
try:
    from tenable.sc import TenableSC
    from tenable.errors import TenableIOError
    TENABLE_AVAILABLE = True
except ImportError:
    TENABLE_AVAILABLE = False
    print("Warning: tenable.sc module not found. Upload functionality will be disabled.")

# --- UTILITY FUNCTIONS ---
def ensure_dir_exists(directory):
    """Ensures a directory exists, creates if not."""
    if not os.path.exists(directory):
        try:
            os.makedirs(directory, exist_ok=True)
            log_message(f"Created directory: {directory}")
        except PermissionError:
            log_message(f"ERROR: No permission to create directory: {directory}")
            return False
        except Exception as e:
            log_message(f"ERROR: Failed to create directory {directory}: {str(e)}")
            return False
    return True

def log_message(message, console_output=True):
    """Writes a message to the log file with a timestamp."""
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_entry = f"[{timestamp}] {message}"

    # Always output to console first
    if console_output:
        print(log_entry)

    # Try to log to file, but don't fail if we can't
    try:
        # Ensure log directory exists
        log_dir = os.path.dirname(LOG_FILE)
        if not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)

        with open(LOG_FILE, 'a', encoding='utf-8') as f:
            f.write(f"{log_entry}\n")
    except PermissionError:
        if console_output:
            print(f"WARNING: No permission to write to log file: {LOG_FILE}")
    except Exception as e:
        if console_output:
            print(f"WARNING: Error writing to log file: {str(e)}")
        # Continue execution even if logging fails

def pretty_print_xml(element):
    """Returns a nicely formatted XML string for the Element Tree."""
    try:
        rough_string = ET.tostring(element, 'utf-8')
        reparsed = minidom.parseString(rough_string)
        return reparsed.toprettyxml(indent="  ")
    except Exception as e:
        log_message(f"ERROR: XML formatting failed: {str(e)}")
        return None

def archive_file(filepath):
    """Moves a processed file to an 'archive' subdirectory with conflict handling."""
    try:
        archive_dir = os.path.join(os.path.dirname(filepath), 'archive')
        if not ensure_dir_exists(archive_dir):
            return False
            
        # Generate unique name if file exists
        dest_path = os.path.join(archive_dir, os.path.basename(filepath))
        if os.path.exists(dest_path):
            # Add timestamp to make filename unique
            filename, ext = os.path.splitext(os.path.basename(filepath))
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            dest_path = os.path.join(archive_dir, f"{filename}_{timestamp}{ext}")
        
        shutil.move(filepath, dest_path)
        log_message(f"Archived {filepath} to {dest_path}")
        return True
    except Exception as e:
        log_message(f"ERROR: Failed to archive {filepath}: {str(e)}")
        return False

# --- CONVERSION LOGIC ---
def map_severity_to_nessus(severity):
    """Maps severity levels to Nessus numerical severity."""
    severity = str(severity).upper() if severity else "INFO"
    severity_mapping = {
        "CRITICAL": "4", "HIGH": "3", "MEDIUM": "2", "LOW": "1", "INFO": "0"
    }
    return severity_mapping.get(severity, "0")

def generate_plugin_id(cve_id):
    """Generates a stable plugin ID from a CVE or vulnerability ID."""
    if not cve_id or cve_id == "N/A":
        # Generate a random but stable ID for non-CVE vulnerabilities
        return str(int(hashlib.md5(str(uuid.uuid4()).encode()).hexdigest(), 16) % 10**8 + 900000000)
    
    # Extract digits from CVE ID and ensure consistent length
    digits = ''.join(filter(str.isdigit, cve_id))
    if len(digits) < 8:
        # Pad with additional digits derived from the CVE string
        hash_val = str(int(hashlib.md5(cve_id.encode()).hexdigest(), 16) % 10**8)
        digits = digits + hash_val[:8-len(digits)]
    
    # Ensure we return at most 8 digits prefixed with '9'
    return "9" + digits[-7:] if len(digits) > 7 else "9" + digits.zfill(7)

def create_from_trivy(json_path, report_name):
    """Creates a .nessus XML structure from a Trivy JSON report."""
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except json.JSONDecodeError as e:
        log_message(f"ERROR: Invalid JSON in {json_path}: {str(e)}")
        return None
    except FileNotFoundError:
        log_message(f"ERROR: File not found: {json_path}")
        return None
    except Exception as e:
        log_message(f"ERROR: Failed to read {json_path}: {str(e)}")
        return None

    # Validate required fields
    if not isinstance(data, dict):
        log_message(f"ERROR: Invalid JSON structure in {json_path} - expected object")
        return None
    
    try:
        nessus_root = ET.Element("NessusClientData_v2")
        report = ET.SubElement(nessus_root, "Report", name=report_name)
        host_name = data.get("ArtifactName", report_name)
        report_host = ET.SubElement(report, "ReportHost", name=host_name)
        host_props = ET.SubElement(report_host, "HostProperties")
        ET.SubElement(host_props, "tag", name="host-fqdn").text = host_name
        ET.SubElement(host_props, "tag", name="operating-system").text = "Container Image"
        ET.SubElement(host_props, "tag", name="host-type").text = "container"
        
        # Add scan metadata
        scan_date = datetime.datetime.now().strftime("%a %b %d %H:%M:%S %Y")
        ET.SubElement(host_props, "tag", name="HOST_START").text = scan_date
        ET.SubElement(host_props, "tag", name="HOST_END").text = scan_date
        
        # Track how many vulnerabilities were processed
        vulnerability_count = 0

        # Validate Results structure
        results = data.get("Results", [])
        if not isinstance(results, list):
            log_message(f"WARNING: Invalid Results structure in {json_path} - expected list")
            results = []

        for result in results:
            if not isinstance(result, dict):
                log_message(f"WARNING: Invalid result item in {json_path} - skipping")
                continue

            vulnerabilities = result.get("Vulnerabilities", [])
            if not isinstance(vulnerabilities, list):
                log_message(f"WARNING: Invalid Vulnerabilities structure in result - skipping")
                continue

            for vuln in vulnerabilities:
                if not isinstance(vuln, dict):
                    log_message(f"WARNING: Invalid vulnerability item - skipping")
                    continue

                vulnerability_count += 1
                vuln_id = vuln.get("VulnerabilityID", f"TRIVY-{vulnerability_count}")
                
                # Create unique plugin ID
                plugin_id = generate_plugin_id(vuln_id)
                
                severity = map_severity_to_nessus(vuln.get("Severity"))
                item = ET.SubElement(report_host, "ReportItem", 
                                    port="0", 
                                    svc_name="general", 
                                    protocol="tcp",
                                    severity=severity,
                                    pluginID=plugin_id, 
                                    pluginName=vuln_id, 
                                    pluginFamily="Trivy Scan")
                
                # Add metadata about the component
                pkg_name = vuln.get("PkgName", "unknown")
                pkg_version = vuln.get("InstalledVersion", "unknown")
                fixed_version = vuln.get("FixedVersion", "N/A")
                
                # Add component as comment to the report host
                report_host.append(ET.Comment(f" Vulnerability in {pkg_name} {pkg_version} "))
                
                # Add description with formatted details
                description = vuln.get("Description", "No description available.")
                component_info = f"Component: {pkg_name}\nInstalled Version: {pkg_version}\nFixed Version: {fixed_version}\n\n"
                ET.SubElement(item, "description").text = component_info + description
                
                # Add solution with specific upgrade advice
                solution_text = f"Upgrade {pkg_name} to version {fixed_version}" if fixed_version != "N/A" else f"No fixed version available for {pkg_name}. Consider using an alternative component."
                ET.SubElement(item, "solution").text = solution_text
                
                # Add synopsis
                ET.SubElement(item, "synopsis").text = f"Vulnerability {vuln_id} detected in {pkg_name} {pkg_version}."
                
                # Add references and links
                if "PrimaryURL" in vuln: 
                    ET.SubElement(item, "see_also").text = vuln["PrimaryURL"]
                
                # Add CVSS score if available
                if "CVSS" in vuln:
                    cvss_data = vuln["CVSS"]
                    # Handle different CVSS structures
                    if "nvd" in cvss_data and "V3Score" in cvss_data["nvd"]:
                        ET.SubElement(item, "cvss_base_score").text = str(cvss_data["nvd"]["V3Score"])
                    elif "nvd" in cvss_data and "V2Score" in cvss_data["nvd"]:
                        ET.SubElement(item, "cvss_base_score").text = str(cvss_data["nvd"]["V2Score"])
                    elif "score" in cvss_data:
                        ET.SubElement(item, "cvss_base_score").text = str(cvss_data["score"])

                    # Add CVSS vector if available
                    if "nvd" in cvss_data and "V3Vector" in cvss_data["nvd"]:
                        ET.SubElement(item, "cvss_vector").text = cvss_data["nvd"]["V3Vector"]
                    elif "nvd" in cvss_data and "V2Vector" in cvss_data["nvd"]:
                        ET.SubElement(item, "cvss_vector").text = cvss_data["nvd"]["V2Vector"]
                    elif "vector" in cvss_data:
                        ET.SubElement(item, "cvss_vector").text = cvss_data["vector"]
                
                # Add plugin output with detection details
                output_text = f"Detected {vuln_id} in {pkg_name} {pkg_version}\n"
                if "Title" in vuln:
                    output_text += f"Title: {vuln['Title']}\n"
                ET.SubElement(item, "plugin_output").text = output_text
        
        log_message(f"Processed {vulnerability_count} vulnerabilities from Trivy report")
        return pretty_print_xml(nessus_root)
    except Exception as e:
        log_message(f"ERROR: Failed to create Nessus XML from Trivy data: {str(e)}")
        log_message(traceback.format_exc())
        return None

def create_from_xray(json_path, report_name):
    """Creates a .nessus XML structure from a JFrog Xray JSON report."""
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except json.JSONDecodeError as e:
        log_message(f"ERROR: Invalid JSON in {json_path}: {str(e)}")
        return None
    except FileNotFoundError:
        log_message(f"ERROR: File not found: {json_path}")
        return None
    except Exception as e:
        log_message(f"ERROR: Failed to read {json_path}: {str(e)}")
        return None

    # Validate required fields
    if not isinstance(data, dict):
        log_message(f"ERROR: Invalid JSON structure in {json_path} - expected object")
        return None
    
    try:
        nessus_root = ET.Element("NessusClientData_v2")
        report = ET.SubElement(nessus_root, "Report", name=report_name)
        
        # Track components for deduplication
        components = {}
        violation_count = 0

        # Validate violations structure
        violations = data.get("violations", [])
        if not isinstance(violations, list):
            log_message(f"WARNING: Invalid violations structure in {json_path} - expected list")
            violations = []

        for violation in violations:
            if not isinstance(violation, dict):
                log_message(f"WARNING: Invalid violation item in {json_path} - skipping")
                continue

            violation_count += 1
            component_name = violation.get("component", f"unknown-component-{violation_count}")
            
            # Create/retrieve the component's report host
            if component_name in components:
                report_host = components[component_name]
            else:
                report_host = ET.SubElement(report, "ReportHost", name=component_name)
                host_props = ET.SubElement(report_host, "HostProperties")
                ET.SubElement(host_props, "tag", name="host-fqdn").text = component_name
                ET.SubElement(host_props, "tag", name="artifact-type").text = "Component"
                components[component_name] = report_host
            
            # Process each CVE
            cve_count = 0
            cves = violation.get("cves", [])
            if not isinstance(cves, list):
                log_message(f"WARNING: Invalid CVEs structure in violation - skipping")
                continue

            for cve in cves:
                if not isinstance(cve, dict):
                    log_message(f"WARNING: Invalid CVE item - skipping")
                    continue

                cve_count += 1
                cve_id = cve.get("cve", f"XRAY-{violation_count}-{cve_count}")
                
                # Generate a unique plugin ID
                plugin_id = generate_plugin_id(cve_id)
                
                severity = map_severity_to_nessus(violation.get("severity"))
                item = ET.SubElement(report_host, "ReportItem", 
                                    port="0", 
                                    svc_name="general", 
                                    protocol="tcp",
                                    severity=severity,
                                    pluginID=plugin_id, 
                                    pluginName=cve_id, 
                                    pluginFamily="JFrog Xray Scan")
                
                # Add more detailed information
                summary = violation.get("summary", "No summary provided.")
                component_version = violation.get("version", "unknown")
                watch_name = violation.get("watch_name", "unknown")
                
                description_text = f"Component: {component_name}\nVersion: {component_version}\nWatch: {watch_name}\n\n{summary}"
                ET.SubElement(item, "description").text = description_text
                
                # Add solution
                solution = cve.get("solution", "See remediation details in the JFrog Platform.")
                ET.SubElement(item, "solution").text = solution
                
                # Add synopsis
                ET.SubElement(item, "synopsis").text = f"Vulnerability {cve_id} detected in {component_name} {component_version}."
                
                # Add CVSS scores if available
                if "cvss_v3_score" in cve: 
                    ET.SubElement(item, "cvss_v3_base_score").text = str(cve["cvss_v3_score"])
                if "cvss_v2_score" in cve:
                    ET.SubElement(item, "cvss_base_score").text = str(cve["cvss_v2_score"])
                
                # Add plugin output with detection details
                output_text = f"Detected {cve_id} in {component_name} {component_version}\n"
                if "issue_id" in violation:
                    output_text += f"Issue ID: {violation['issue_id']}\n"
                ET.SubElement(item, "plugin_output").text = output_text
        
        log_message(f"Processed {violation_count} violations from Xray report")
        return pretty_print_xml(nessus_root)
    except Exception as e:
        log_message(f"ERROR: Failed to create Nessus XML from Xray data: {str(e)}")
        log_message(traceback.format_exc())
        return None

# --- TENABLE.SC UPLOAD ---
def upload_to_tenable_sc(nessus_content, filename_prefix):
    """Uploads a .nessus file to Tenable.sc and logs the action."""
    if not TENABLE_AVAILABLE:
        log_message("ERROR: Tenable.sc Python module not available. Skipping upload.")
        return False
        
    if not nessus_content:
        log_message("ERROR: No valid Nessus content to upload.")
        return False
    
    # Create a unique filename
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    nessus_filename = f"{filename_prefix}_{timestamp}.nessus"
    temp_file_path = os.path.join(os.path.dirname(REPORTS_DIR), 'temp', nessus_filename)
    
    # Ensure temp directory exists
    temp_dir = os.path.dirname(temp_file_path)
    if not ensure_dir_exists(temp_dir):
        return False
    
    log_message(f"Attempting to upload {nessus_filename} to Tenable.sc...")
    try:
        # Write the temporary file
        with open(temp_file_path, 'w') as f:
            f.write(nessus_content)

        # Connect to Tenable.sc and upload
        sc = TenableSC(SC_HOST)
        sc.login(access_key=ACCESS_KEY, secret_key=SECRET_KEY)
        
        with open(temp_file_path, 'rb') as f_binary:
            upload_response = sc.scan_results.import_scan(f_binary, repo_id=REPO_ID)
            log_message(f"SUCCESS: Uploaded {nessus_filename} to repository ID {REPO_ID}")
            log_message(f"Upload response: {upload_response}")
            
        sc.logout()
        return True
    except Exception as e:
        # Check if it's a TenableIOError if the module is available
        if TENABLE_AVAILABLE and 'TenableIOError' in str(type(e)):
            log_message(f"ERROR: Tenable.sc API error: {str(e)}")
        else:
            log_message(f"ERROR: Failed to upload to Tenable.sc. Reason: {str(e)}")
            log_message(traceback.format_exc())
    finally:
        # Clean up the temp file regardless of success
        try:
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)
                log_message(f"Removed temporary file: {temp_file_path}")
        except Exception as e:
            log_message(f"Warning: Failed to clean up temporary file: {str(e)}")
    
    return False

# --- MAIN WORKFLOW ---
def process_directory(report_type, converter_func):
    """Process all JSON files in a directory with the specified converter function."""
    report_dir = os.path.join(REPORTS_DIR, report_type)
    
    # Check if directory exists
    if not os.path.exists(report_dir):
        log_message(f"WARNING: Directory does not exist: {report_dir}")
        return 0
    
    # Ensure archive directory exists
    archive_dir = os.path.join(report_dir, 'archive')
    ensure_dir_exists(archive_dir)
    
    processed_count = 0
    for filename in os.listdir(report_dir):
        if filename.endswith('.json') and os.path.isfile(os.path.join(report_dir, filename)):
            filepath = os.path.join(report_dir, filename)
            log_message(f"Processing {report_type} report: {filename}")
            
            # Generate report name with timestamp to ensure uniqueness
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            report_name = f"{report_type.capitalize()}_{os.path.splitext(filename)[0]}_{timestamp}"
            
            # Convert to Nessus format
            nessus_data = converter_func(filepath, report_name)
            
            if nessus_data:
                # Save a copy of the Nessus file (optional)
                # nessus_path = os.path.join(report_dir, f"{os.path.splitext(filename)[0]}.nessus")
                # with open(nessus_path, 'w') as f:
                #     f.write(nessus_data)
                
                # Upload to Tenable.sc
                if upload_to_tenable_sc(nessus_data, report_name):
                    if archive_file(filepath):
                        processed_count += 1
                    else:
                        log_message(f"WARNING: File processed but archiving failed: {filepath}")
            else:
                log_message(f"ERROR: Failed to convert {filepath} to Nessus format")
    
    return processed_count

def main():
    """Main function to find reports, convert them, and upload them."""
    log_message("Starting importer run...")
    
    # Ensure required directories exist
    for dir_path in [REPORTS_DIR, os.path.join(REPORTS_DIR, 'trivy'), 
                    os.path.join(REPORTS_DIR, 'xray'), LOG_DIR]:
        ensure_dir_exists(dir_path)
    
    # Process reports
    trivy_count = process_directory('trivy', create_from_trivy)
    log_message(f"Processed {trivy_count} Trivy reports")
    
    xray_count = process_directory('xray', create_from_xray)
    log_message(f"Processed {xray_count} Xray reports")
    
    total_count = trivy_count + xray_count
    log_message(f"Importer run finished. Total reports processed: {total_count}")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        log_message(f"CRITICAL ERROR: Unhandled exception in main program: {str(e)}")
        log_message(traceback.format_exc())
        sys.exit(1)